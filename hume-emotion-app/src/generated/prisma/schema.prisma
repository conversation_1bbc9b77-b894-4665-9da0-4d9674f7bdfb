// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Result {
  id           String   @id @default(uuid())
  inputText    String // The transcript from the voice recording
  analysisText String // The AI-generated insights
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("results")
}
