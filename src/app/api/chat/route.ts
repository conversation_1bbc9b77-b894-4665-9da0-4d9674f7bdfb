import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages } = await req.json();

  const openrouterApiKey = process.env.OPENROUTER_API_KEY;
  if (!openrouterApiKey) {
    return new Response('OpenRouter API key not configured', { status: 500 });
  }

  // Create OpenAI client configured for OpenRouter
  const client = openai({
    apiKey: openrouterApiKey,
    baseURL: 'https://openrouter.ai/api/v1',
  });

  const result = await streamText({
    model: client('anthropic/claude-3.5-sonnet-20241022'),
    messages,
    system: `You are an expert emotional intelligence coach and conversational AI. You help people understand their emotions and provide insights about emotional patterns in their speech and communication.

You have access to the user's voice analysis data including:
- Transcript of what they said
- Top 10 emotions detected in their voice with intensity scores
- Previous AI insights about their emotional patterns

Your role is to:
1. Answer questions about their emotional analysis
2. Provide deeper insights into their emotional patterns
3. Help them understand what their emotions might reveal about their thoughts and feelings
4. Offer practical advice for emotional awareness and growth
5. Engage in meaningful conversation about emotions and communication

Be supportive, insightful, and educational. Focus on helping them develop better emotional self-awareness.`,
  });

  return result.toDataStreamResponse();
}
